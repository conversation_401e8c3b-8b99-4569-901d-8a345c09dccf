{"version": "1.0.0", "source": "./index.html", "type": "module", "name": "huitong-3d", "description": "会通3D模型展示和材质定制平台", "scripts": {"dev": "vite --port 3000 --host localhost", "build": "vite build", "upload-models": "node scripts/upload-sample-models.js", "start": "npm run stop && npm run dev", "stop": "node stop-dev.js", "preview": "vite preview --port 3000", "status": "lsof -i :3000 || echo '没有进程在3000端口运行'"}, "dependencies": {"@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@react-three/drei": "^9.99.7", "@react-three/fiber": "^8.15.19", "canvas": "^3.1.0", "class-variance-authority": "^0.7.0", "clsx": "2.1.1", "dotenv": "^16.5.0", "gl": "^8.1.6", "lucide-react": "^0.453.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.1", "tailwind-merge": "2.5.4", "terser": "^5.40.0", "three": "^0.175.0"}, "devDependencies": {"@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@vitejs/plugin-react": "4.3.4", "esbuild": "0.24.0", "globals": "15.12.0", "tailwindcss": "3.4.16", "vite": "6.0.4"}, "alias": {"@/*": "./src/components/ui/$1"}}