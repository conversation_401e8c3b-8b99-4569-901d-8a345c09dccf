# 开发环境使用指南

## 简化的开发服务器

我们已经简化了开发环境的配置，现在只需要一个命令即可启动开发服务器。

### 启动开发服务器

```bash
npm run start
```

这个命令会：
1. 自动停止任何已运行的开发服务器
2. 启动新的Vite开发服务器在3000端口
3. 提供热模块替换(HMR)功能

服务器启动后，访问 http://localhost:3000 即可查看应用。

### 停止开发服务器

如果您需要停止开发服务器，可以运行：

```bash
npm run stop
```

或者直接在终端中按 `Ctrl+C`。

### 检查服务器状态

```bash
npm run status
```

这个命令会显示3000端口的占用情况，帮助您确认服务器是否正在运行。

## 热重载功能

Vite提供了强大的热模块替换(HMR)功能，它可以在不刷新整个页面的情况下，只更新修改的部分。这意味着：

1. 修改React组件时，组件会自动更新，而不会丢失组件状态
2. 修改CSS/样式时，样式会立即应用，无需刷新页面
3. 修改配置文件时，可能需要重启服务器

## 注意事项

1. 开发服务器固定使用3000端口，如果端口被占用会自动停止占用进程
2. 如果您修改了Vite配置文件(`vite.config.ts`)，需要重启开发服务器
3. 如果热重载不工作，请尝试手动刷新浏览器

## 故障排除

如果开发服务器无法正常启动，您可以：

1. 运行 `npm run stop` 确保停止所有相关进程
2. 删除 `.dev-server-lock` 文件（如果存在）
3. 重新运行 `npm run start`

如果仍有问题，可以手动检查端口占用：
```bash
lsof -i :3000  # 查看3000端口占用情况
```
